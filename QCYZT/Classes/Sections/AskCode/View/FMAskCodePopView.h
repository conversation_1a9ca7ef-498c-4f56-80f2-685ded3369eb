//
//  FMAskCodePopView.h
//  QCYZT
//
//  Created by Augment on 2025-01-11.
//  Copyright © 2025 LZKJ. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "FMCouponTableModel.h"
#import "FMTextView.h"
#import "FMSearchStockCell.h"

@interface FMAskCodePopView : UIView

@property (nonatomic, weak) UILabel *publisLb;
@property (nonatomic, copy) NSString *placeholderText;
@property (nonatomic, weak) FMTextView *textView;
@property (nonatomic, weak) UITextField *stockTF;

@property (nonatomic, assign) NSInteger limitWordNum;
@property (nonatomic, weak) UIButton *publishBtn;

/// 问股专用回调
@property (nonatomic,copy) void (^publishAskCodeBlock) (NSString *content, FMSearchStockModel *stockModel);

/// 投顾id
@property (nonatomic,copy) NSString *bignameId;
/// 1 笔记 2 问股 3 私信 4 偷听 5 直播 6 优质课程 7 系列课程
@property (nonatomic,assign) NSInteger consumeType;
/// 内容id
@property (nonatomic,assign) NSInteger contentId;
/// 卡券id
@property (nonatomic,copy) NSString *couponId;
/// 卡券类型
@property (nonatomic,copy) NSString *type;
/// 提问价格
@property (nonatomic,copy) NSString *askPrice;
/// 支持表情
@property(nonatomic,assign) BOOL supportEmoji;

@property (nonatomic, strong) FMCouponTableModel *selectedCoupon; // 选择的卡券

/// 选择的股票
@property (nonatomic, strong) FMSearchStockModel *choosedStockModel;

/// 请求卡券列表
- (void)requestCouponList;

@end
