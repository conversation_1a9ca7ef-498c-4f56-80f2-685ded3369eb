//
//  FMAskCodePopViewTest.m
//  QCYZT
//
//  Created by Augment on 2025-01-11.
//  Copyright © 2025 LZKJ. All rights reserved.
//

#import <XCTest/XCTest.h>
#import "FMAskCodePopView.h"
#import "FMCommentView.h"

@interface FMAskCodePopViewTest : XCTestCase

@end

@implementation FMAskCodePopViewTest

- (void)setUp {
    [super setUp];
}

- (void)tearDown {
    [super tearDown];
}

- (void)testFMAskCodePopViewInitialization {
    // 测试FMAskCodePopView初始化
    FMAskCodePopView *askCodeView = [[FMAskCodePopView alloc] initWithFrame:CGRectMake(0, 0, 375, 667)];
    
    XCTAssertNotNil(askCodeView, @"FMAskCodePopView应该能够正常初始化");
    XCTAssertNotNil(askCodeView.textView, @"textView应该存在");
    XCTAssertNotNil(askCodeView.stockTF, @"stockTF应该存在");
    XCTAssertNotNil(askCodeView.publishBtn, @"publishBtn应该存在");
    XCTAssertNotNil(askCodeView.publisLb, @"publisLb应该存在");
}

- (void)testFMCommentViewInitialization {
    // 测试FMCommentView初始化（通用回复功能）
    FMCommentView *commentView = [[FMCommentView alloc] initWithFrame:CGRectMake(0, 0, 375, 667)];
    
    XCTAssertNotNil(commentView, @"FMCommentView应该能够正常初始化");
    XCTAssertNotNil(commentView.textView, @"textView应该存在");
    XCTAssertNotNil(commentView.publishBtn, @"publishBtn应该存在");
    XCTAssertNotNil(commentView.publisLb, @"publisLb应该存在");
}

- (void)testFMAskCodePopViewProperties {
    // 测试FMAskCodePopView的问股特有属性
    FMAskCodePopView *askCodeView = [[FMAskCodePopView alloc] initWithFrame:CGRectMake(0, 0, 375, 667)];
    
    // 设置问股相关属性
    askCodeView.bignameId = @"test_bigname_id";
    askCodeView.consumeType = 2;
    askCodeView.askPrice = @"10.0";
    askCodeView.placeholderText = @"请输入您的问题";
    
    XCTAssertEqualObjects(askCodeView.bignameId, @"test_bigname_id", @"bignameId应该正确设置");
    XCTAssertEqual(askCodeView.consumeType, 2, @"consumeType应该正确设置");
    XCTAssertEqualObjects(askCodeView.askPrice, @"10.0", @"askPrice应该正确设置");
    XCTAssertEqualObjects(askCodeView.placeholderText, @"请输入您的问题", @"placeholderText应该正确设置");
}

- (void)testFMCommentViewProperties {
    // 测试FMCommentView的通用属性
    FMCommentView *commentView = [[FMCommentView alloc] initWithFrame:CGRectMake(0, 0, 375, 667)];
    
    // 设置通用属性
    commentView.placeholderText = @"请输入评论内容";
    commentView.limitWordNum = 200;
    commentView.supportEmoji = YES;
    
    XCTAssertEqualObjects(commentView.placeholderText, @"请输入评论内容", @"placeholderText应该正确设置");
    XCTAssertEqual(commentView.limitWordNum, 200, @"limitWordNum应该正确设置");
    XCTAssertTrue(commentView.supportEmoji, @"supportEmoji应该正确设置");
}

- (void)testCallbackFunctionality {
    // 测试回调功能
    FMAskCodePopView *askCodeView = [[FMAskCodePopView alloc] initWithFrame:CGRectMake(0, 0, 375, 667)];
    FMCommentView *commentView = [[FMCommentView alloc] initWithFrame:CGRectMake(0, 0, 375, 667)];
    
    __block BOOL askCodeCallbackCalled = NO;
    __block BOOL commentCallbackCalled = NO;
    
    // 设置问股回调
    askCodeView.publishAskCodeBlock = ^(NSString *content, FMSearchStockModel *stockModel) {
        askCodeCallbackCalled = YES;
    };
    
    // 设置通用回复回调
    commentView.publishBlock = ^(NSString *content) {
        commentCallbackCalled = YES;
    };
    
    XCTAssertNotNil(askCodeView.publishAskCodeBlock, @"问股回调应该能够设置");
    XCTAssertNotNil(commentView.publishBlock, @"通用回复回调应该能够设置");
}

@end
