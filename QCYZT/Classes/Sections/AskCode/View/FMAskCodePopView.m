//
//  FMAskCodePopView.m
//  QCYZT
//
//  Created by Augment on 2025-01-11.
//  Copyright © 2025 LZKJ. All rights reserved.
//

#import "FMAskCodePopView.h"
#import "NSString+emoji.h"
#import "IQKeyboardManager.h"
#import "FMPayMentCouponSelectVC.h"
#import "HttpRequestTool+UserCenter.h"
#import "FMAskCodeStockResultTableView.h"
#import "CacheRequestManager.h"
#import "HttpRequestTool+Search.h"
#import "FMNewSearchStockModel.h"
#import "NSString+characterJudge.h"

@interface FMAskCodePopView() <UITextViewDelegate>

@property (nonatomic,weak) UIView *contentView;
@property (nonatomic,weak) UIButton *cancelBtn;

@property (nonatomic, strong) UIView *couponBgView;
@property (nonatomic, strong) UILabel *couponNameLB;
@property (nonatomic, strong) UILabel *couponValueLB;
@property (nonatomic, strong) UIImageView *couponArrowImgV;
@property (nonatomic, strong) ZLTagLabel *firstRechargeLabel;              // 首充优惠
@property (nonatomic, strong) NSArray<FMCouponTableModel *> *availableCoupons; // 可用卡券列表
@property (nonatomic, strong) UIStackView *stackView;

/// 股票结果View
@property (nonatomic, strong) FMAskCodeStockResultTableView *stockResultView;
@property (nonatomic, strong) UIScrollView *inputViewContentView;
@property (nonatomic, strong) UIView *resultBackView;

@end

@implementation FMAskCodePopView

- (id)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [IQKeyboardManager sharedManager].shouldResignOnTouchOutside = false;
        [self setUp];
    }
    return self;
}

- (void)willMoveToSuperview:(UIView *)newSuperview {
    [super willMoveToSuperview:newSuperview];
    
    [IQKeyboardManager sharedManager].enableAutoToolbar = false;
}

- (void)removeFromSuperview {
    [super removeFromSuperview];
    [IQKeyboardManager sharedManager].shouldResignOnTouchOutside = true;
    [IQKeyboardManager sharedManager].enableAutoToolbar = true;
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)setUp {
    UIView *bgView = [[UIView alloc] init];
    [self addSubview:bgView];
    [bgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
    bgView.backgroundColor = ColorWithHexAlpha(0x000000, 0.5);
    [bgView addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(removeFromSuperview)]];
    
    UIView *contentView = [[UIView alloc] init];
    [self addSubview:contentView];
    [contentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.bottom.equalTo(@(0));
        make.height.equalTo(336);
    }];
    contentView.backgroundColor = UIColor.up_contentBgColor;
    [contentView layerAndBezierPathWithRect:CGRectMake(0, 0, UI_SCREEN_WIDTH, 366) cornerRadii:CGSizeMake(10, 10) byRoundingCorners:UIRectCornerTopLeft | UIRectCornerTopRight];
    self.contentView = contentView;
    
    UILabel *publishLb = [[UILabel alloc] init];
    [contentView addSubview:publishLb];
    [publishLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(15);
        make.top.equalTo(13);
    }];
    publishLb.font = FontWithSize(16);
    publishLb.textColor = UIColor.up_textPrimaryColor;
    publishLb.text = @"发布";
    self.publisLb = publishLb;
    
    UIButton *cancelBtn = [[UIButton alloc] init];
    [contentView addSubview:cancelBtn];
    [cancelBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(-15);
        make.centerY.equalTo(publishLb);
        make.width.height.equalTo(30);
    }];
    [cancelBtn setImage:ImageWithName(@"关闭") forState:UIControlStateNormal];
    [cancelBtn addTarget:self action:@selector(cancel) forControlEvents:UIControlEventTouchUpInside];
    self.cancelBtn = cancelBtn;
    
    [self setupStackView];
    [self setupPublishButton];
}

- (void)setupStackView {
    UIStackView *stackView = [[UIStackView alloc] init];
    stackView.axis = UILayoutConstraintAxisVertical;
    stackView.distribution = UIStackViewDistributionFill;
    stackView.alignment = UIStackViewAlignmentFill;
    stackView.spacing = 0;
    [self.contentView addSubview:stackView];
    [stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(15);
        make.right.equalTo(-15);
        make.top.equalTo(self.publisLb.mas_bottom).offset(15);
    }];
    self.stackView = stackView;
    
    // 添加股票搜索输入框
    [self setupStockInputField];
    
    // 添加文本输入框
    [self setupTextView];
    
    // 添加卡券选择区域
    [self setupCouponView];
}

- (void)setupStockInputField {
    UILabel *title = [[UILabel alloc] init];
    title.text = @"咨询股票";
    title.font = FontWithSize(16);
    title.textColor = UIColor.up_textPrimaryColor;
    [self.stackView addArrangedSubview:title];
    [self.stackView setCustomSpacing:8 afterView:title];
    
    UITextField *stockTF = [[UITextField alloc] init];
    stockTF.font = FontWithSize(16);
    stockTF.backgroundColor = UIColor.fm_F7F7F7_2E2F33;
    stockTF.clearButtonMode = UITextFieldViewModeWhileEditing;
    stockTF.attributedPlaceholder = [[NSMutableAttributedString alloc] initWithString:@"请输入股票名称或代码" attributes:@{NSFontAttributeName : FontWithSize(16), NSForegroundColorAttributeName : UIColor.fm_BFBFBF_888888}];
    UI_View_Radius(stockTF, 5);
    stockTF.leftViewMode = UITextFieldViewModeAlways;
    stockTF.leftView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, 15, 45)];
    [self.stackView insertArrangedSubview:stockTF atIndex:1];
    [stockTF mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(45);
    }];
    self.stockTF = stockTF;
    
    [self.stackView setCustomSpacing:15 afterView:stockTF];
    
    // 添加股票搜索功能
    [self setupStockSearch];
}

- (void)setupTextView {
    FMTextView *textView = [[FMTextView alloc] init];
    textView.font = FontWithSize(16);
    textView.backgroundColor = UIColor.fm_F7F7F7_2E2F33;
    textView.placehoderColor = UIColor.fm_BFBFBF_888888;
    textView.textColor = UIColor.up_textPrimaryColor;
    UI_View_BorderRadius(textView, 5, 1, ColorWithHex(0xe5e5e5));
    textView.delegate = self;
    textView.textContainerInset = UIEdgeInsetsMake(8, 10, 8, 0);
    [self.stackView addArrangedSubview:textView];
    [textView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(150);
    }];
    self.textView = textView;
    
    [self.stackView setCustomSpacing:15 afterView:textView];
}

- (void)setupCouponView {
    // 卡券选择区域
    UILabel *couponTitle = [[UILabel alloc] init];
    couponTitle.text = @"选择卡券";
    couponTitle.font = FontWithSize(16);
    couponTitle.textColor = UIColor.up_textPrimaryColor;
    [self.stackView addArrangedSubview:couponTitle];
    [self.stackView setCustomSpacing:8 afterView:couponTitle];

    [self.stackView addArrangedSubview:self.couponBgView];
    [self.couponBgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(33);
    }];
    [self.stackView setCustomSpacing:15 afterView:self.couponBgView];
}

- (void)setupPublishButton {
    UIButton *publishBtn = [[UIButton alloc] init];
    [self.contentView addSubview:publishBtn];
    [publishBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(15);
        make.right.equalTo(-15);
        make.bottom.equalTo(-30);
        make.height.equalTo(45);
    }];
    publishBtn.backgroundColor = ColorWithHex(0xababab);
    publishBtn.userInteractionEnabled = NO;
    [publishBtn setTitle:@"发布" forState:UIControlStateNormal];
    [publishBtn setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
    publishBtn.titleLabel.font = FontWithSize(16);
    UI_View_Radius(publishBtn, 5);
    [publishBtn addTarget:self action:@selector(publish) forControlEvents:UIControlEventTouchUpInside];
    self.publishBtn = publishBtn;

    // 添加首充优惠标签
    [self.contentView addSubview:self.firstRechargeLabel];
    [self.firstRechargeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(publishBtn);
        make.top.equalTo(publishBtn.mas_top).offset(-12.5);
        make.height.equalTo(25);
    }];
    self.firstRechargeLabel.hidden = YES;

    // 添加通知监听
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(keyboardWillChangeFrame:) name:UIKeyboardWillChangeFrameNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(removeFromSuperview) name:kALiMessageAffirm object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(uptheme_themeDidUpdate) name:kUPThemeDidChangeNotification object:nil];
}

- (void)setupStockSearch {
    // 添加股票搜索输入框的事件监听
    [self.stockTF addTarget:self action:@selector(textFieldDidChange:) forControlEvents:UIControlEventEditingChanged];

    // 添加股票搜索结果视图
    [self addSubview:self.resultBackView];
    [self.resultBackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.stockTF.mas_bottom).offset(5);
        make.left.equalTo(15);
        make.right.equalTo(-15);
    }];

    [self.resultBackView addSubview:self.stockResultView];
    [self.stockResultView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.insets(UIEdgeInsetsZero);
    }];
}

#pragma mark - Actions

- (void)cancel {
    [self removeFromSuperview];
}

- (void)publish {
    if ([FMHelper checkLoginStatus]) {
        if ([self.publishBtn.titleLabel.text containsString:@"余额不足"]) {
            [ProtocolJump jumpWithUrl:@"qcyzt://recharge"];
            [self cancel];
        } else {
            if ([self characterJudge]) {
                [self removeFromSuperview];
                WEAKSELF;
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    if (__weakSelf.publishAskCodeBlock) {
                        __weakSelf.publishAskCodeBlock([NSString stringWithFormat:@"咨询%@,%@",__weakSelf.stockTF.text,__weakSelf.textView.text], __weakSelf.choosedStockModel);
                    }
                });
            }
        }
    }
}

#pragma mark - UITextViewDelegate
-(void)textViewDidChange:(UITextView *)textView{
    if (textView.text.length<=0) {
        if ([self.publishBtn.titleLabel.text containsString:@"余额不足"]) {
            self.publishBtn.backgroundColor = FMNavColor;
            self.publishBtn.userInteractionEnabled = YES;
        } else {
            self.publishBtn.backgroundColor = ColorWithHex(0xababab);
            self.publishBtn.userInteractionEnabled = NO;
        }
    }else{
        if ([textView.text isContainEmoji] && !self.supportEmoji) {
            [SVProgressHUD showErrorWithStatus:@"暂不支持输入表情"];
            return;
        }

        NSInteger limitNum = self.limitWordNum;
        if (limitNum == 0) {
            limitNum = 100;
        }
        if (textView.text.length > limitNum)
        {
            textView.text = [textView.text substringToIndex:limitNum];
            [SVProgressHUD showInfoWithStatus:[NSString stringWithFormat:@"最多输入%zd个字", limitNum]];
        }

        if (self.askPrice.length > 0 && self.stockTF.text.length != 0) {
            self.publishBtn.backgroundColor = FMNavColor;
            self.publishBtn.userInteractionEnabled = YES;
        } else {
            self.publishBtn.backgroundColor = FMNavColor;
            self.publishBtn.userInteractionEnabled = YES;
        }
    }
}

- (BOOL)textView:(UITextView *)textView shouldChangeTextInRange:(NSRange)range replacementText:(NSString *)text {
    if ([text isContainEmoji] && !self.supportEmoji) {
        [textView resignFirstResponder];
        [SVProgressHUD showErrorWithStatus:@"暂不支持输入表情"];
        return NO;
    }
    return YES;;
}

- (void)textFieldDidChange:(UITextField *)textField {
    if (textField.text.length == 0) {
        self.choosedStockModel = nil;
    }

    if (textField.text.length > 8) {
        textField.text = [textField.text substringToIndex:8];
    }
    [self delaySearch];
}

#pragma mark - Helper Methods

- (BOOL)characterJudge {
    NSString *inputStr = [self.textView.text stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
    if (!inputStr.length) {
        [SVProgressHUD showErrorWithStatus:@"输入内容不能为空~"];
        return NO;
    }
    if (self.stockTF && (!self.stockTF.text.length || !self.choosedStockModel)) {
        [SVProgressHUD showErrorWithStatus:@"请输入并选择您想咨询的股票"];
        return NO;
    }

    if ([self.textView.text isContainEmoji] && !self.supportEmoji) {
        [SVProgressHUD showErrorWithStatus:@"暂不支持输入表情"];
        return NO;
    }

    return YES;
}

- (void)didMoveToSuperview {
    if (self.askPrice.length == 0) {
        [self.textView becomeFirstResponder];
    } else {
        [self.stockTF becomeFirstResponder];
    }
}

// 对textField文字更新做0.5秒延迟才去搜索，减轻服务器压力
- (void)delaySearch {
    [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(newSearchKey) object:nil];
    [self performSelector:@selector(newSearchKey) withObject:nil afterDelay:0.5];
}

- (void)newSearchKey {
    if (self.stockTF.text.length) {
        NSString *whereCondition = nil;
        if ([self.stockTF.text isPureNumber]) {
            whereCondition = [NSString stringWithFormat:@"stockName LIKE '%%%@%%' OR stockCode LIKE '%%%@%%'", self.stockTF.text, self.stockTF.text];
        } else if ([self.stockTF.text isPureAlphabet]) {
            whereCondition = [NSString stringWithFormat:@"stockName LIKE '%%%@%%' OR chiSpelling LIKE '%%%@%%'", self.stockTF.text, self.stockTF.text.uppercaseString];
        } else {
            whereCondition = [NSString stringWithFormat:@"stockName LIKE '%%%@%%' OR stockCode LIKE '%%%@%%' OR chiSpelling LIKE '%%%@%%'", self.stockTF.text, self.stockTF.text, self.stockTF.text.uppercaseString];
        }

        NSArray *allStocks = [DBManager selectDatasWithClass:[FMNewSearchStockModel class] where:whereCondition order:nil limit:@"200"];

        NSMutableArray <FMSearchStockModel *> *arr = [NSMutableArray array];
        for (FMNewSearchStockModel *stockModel in allStocks) {
            FMSearchStockModel *model = [FMSearchStockModel new];
            model.name = stockModel.stockName;
            model.oldStockCode = stockModel.stockCode;
            UPMarketCodeMatchInfo *info = [FMUPDataTool matchInfoWithSetCodeAndCode:stockModel.stockCode];
            model.code = info.code;
            model.setCode = info.setCode;
            model.category = info.category;
            model.origCategory = info.origCategory;

            [arr addObject:model];
        }
        self.resultBackView.hidden = arr.count == 0;
        self.stockResultView.dataArr = arr;
        if (!self.stockResultView.hidden) {
            [self.stockResultView mas_updateConstraints:^(MASConstraintMaker *make) {
                make.height.equalTo(arr.count > 4 ? 120 : 35 * arr.count);
            }];
            self.resultBackView.size = CGSizeMake(UI_SCREEN_WIDTH - 30, arr.count > 4 ? 120 : 35 * arr.count);
            self.resultBackView.layer.masksToBounds = NO;
            self.resultBackView.layer.shadowColor = ColorWithHexAlpha(0x000000, 0.16).CGColor;
            self.resultBackView.layer.shadowOffset = CGSizeMake(0,0);
            self.resultBackView.layer.shadowRadius = 5;
            self.resultBackView.layer.shadowOpacity = 1;
        }
    }
}

#pragma mark - Notification
- (void)keyboardWillChangeFrame:(NSNotification *)notification
{
    NSDictionary *userInfo = notification.userInfo;
    double duration = [userInfo[UIKeyboardAnimationDurationUserInfoKey] doubleValue];
    CGRect keyboardF = [userInfo[UIKeyboardFrameEndUserInfoKey] CGRectValue];

    [UIView animateWithDuration:duration animations:^{
        if (keyboardF.origin.y < UI_SCREEN_HEIGHT) {
            self.transform = CGAffineTransformMakeTranslation(0, - keyboardF.size.height);
        } else {
            self.transform = CGAffineTransformMakeTranslation(0,  0);
        }
    }];
}

- (void)uptheme_themeDidUpdate {
    // 主题更新处理
}

- (void)layoutSubviews {
    [super layoutSubviews];

    if (!self.firstRechargeLabel.hidden) {
        self.firstRechargeLabel.backgroundColor = [UIColor lz_gradientColors:@[ColorWithHex(0xF5CE62), ColorWithHex(0xFFE59B)] withFrame:self.firstRechargeLabel.bounds direction:GradientDirectionLeftToRight];
        [self.firstRechargeLabel layerAndBezierPathWithRect:self.firstRechargeLabel.bounds cornerRadii:CGSizeMake(12.5, 12.5) byRoundingCorners:UIRectCornerTopLeft|UIRectCornerTopRight|UIRectCornerBottomRight];
    }
}

#pragma mark - Public Methods

- (void)requestCouponList {
    [HttpRequestTool requestPaymentCouponListWithBignameId:self.bignameId consumeType:self.consumeType contentId:self.contentId start:^{
        [SVProgressHUD show];
    } failure:^{
        [SVProgressHUD dismiss];
    } success:^(NSDictionary *dic) {
        [SVProgressHUD dismiss];
        if ([dic[@"status"] isEqualToString:@"1"]) {
            self.availableCoupons = [NSArray modelArrayWithClass:[FMCouponTableModel class] json:dic[@"data"]];
            [self configCouponNameAndValueShow];
        }
    }];
}

- (void)configCouponNameAndValueShow {
    if (self.selectedCoupon) {
        self.couponValueLB.text = [NSString stringWithFormat:@"-%.0f金币", self.selectedCoupon.value];
        if (self.selectedCoupon.name.length > 8) {
            self.couponNameLB.text = [NSString stringWithFormat:@"%@...",[self.selectedCoupon.name substringToIndex:8]];
        } else {
            self.couponNameLB.text = self.selectedCoupon.name;
        }
        self.couponNameLB.textColor = UIColor.up_textPrimaryColor;
        self.couponArrowImgV.image = [UIImage imageWithTintColor:FMNavColor blendMode:kCGBlendModeDestinationIn WithImageObject:ImageWithName(@"userCenter_arrow")];
        self.couponBgView.backgroundColor = FMClearColor;
        [self.couponBgView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(0);
        }];
        self.couponId = self.selectedCoupon.couponId;
        self.type = self.selectedCoupon.goodsType;
    } else {
        CGFloat maxAvailableCouponValue = -1;
        for (FMCouponTableModel *model in self.availableCoupons) {
            if (model.isEnable.boolValue) {
                if (model.value > maxAvailableCouponValue) {
                    maxAvailableCouponValue = model.value;
                }
            }
        }

        self.couponValueLB.text = @"";
        self.couponId= @"";
        self.type = @"";
        if (maxAvailableCouponValue == -1) { // 没有可用的卡券
            self.couponNameLB.text = @"请选择";
            self.couponNameLB.textColor = UIColor.fm_BFBFBF_888888;
            self.couponArrowImgV.image = ImageWithName(@"userCenter_arrow");
            self.couponBgView.backgroundColor = FMClearColor;
            [self.couponBgView mas_updateConstraints:^(MASConstraintMaker *make) {
                make.right.equalTo(0);
            }];
        } else {
            self.couponNameLB.text = [NSString stringWithFormat:@"未选卡券，最高抵扣%.0f金币", maxAvailableCouponValue];
            self.couponNameLB.textColor = FMNavColor;
            self.couponArrowImgV.image = [UIImage imageWithTintColor:FMNavColor blendMode:kCGBlendModeDestinationIn WithImageObject:ImageWithName(@"userCenter_arrow")];
            self.couponBgView.backgroundColor = ColorWithHex(0xffebeb);
            [self.couponBgView mas_updateConstraints:^(MASConstraintMaker *make) {
                make.right.equalTo(-15);
            }];
        }
    }
}

#pragma mark - Setter/Getter
- (void)setPlaceholderText:(NSString *)placeholderText {
    _placeholderText = placeholderText;
    self.textView.placehoder = placeholderText;
}

- (void)setAskPrice:(NSString *)askPrice {
    _askPrice = askPrice;
    CGFloat price = askPrice.floatValue;
    if (self.selectedCoupon) {
        price = MAX(price - self.selectedCoupon.value, 0);
    }
    FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
    NSString *firstRechargeText = [FMUserDefault getSeting:AppInit_FirstRecharge_Text];
    if (userModel.coin >= price) {
        [self.publishBtn setTitle:[NSString stringWithFormat:@"发送(%.0f金币)", price] forState:UIControlStateNormal];
        self.firstRechargeLabel.hidden = YES;
    } else {
        [self.publishBtn setTitle:@"余额不足，请先充值" forState:UIControlStateNormal];
        self.publishBtn.backgroundColor = FMNavColor;
        self.publishBtn.userInteractionEnabled = YES;
        if (userModel.isShowFirstDesc && firstRechargeText.length) {
            self.firstRechargeLabel.hidden = NO;
            self.firstRechargeLabel.text = firstRechargeText;
            // 此处不做延时的话layoutSubViews里无法获取firstRechargeLabel正确的frame
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.25 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [self setNeedsLayout];
                [self layoutIfNeeded];
            });
        } else {
            self.firstRechargeLabel.hidden = YES;
        }
    }
}

- (UIView *)couponBgView {
    if (!_couponBgView) {
        _couponBgView = [UIView new];
        _couponBgView.backgroundColor = ColorWithHex(0xffebeb);
        UI_View_Radius(_couponBgView, 16.5);

        [_couponBgView addSubview:self.couponArrowImgV];
        [self.couponArrowImgV mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(0);
            make.right.equalTo(-15);
        }];

        [_couponBgView addSubview:self.couponValueLB];
        [self.couponValueLB mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(0);
            make.right.equalTo(self.couponArrowImgV.mas_left).offset(-5);
        }];

        [_couponBgView addSubview:self.couponNameLB];
        [self.couponNameLB mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(self.couponValueLB.mas_left).offset(0);
            make.centerY.equalTo(0);
            make.left.equalTo(15);
        }];

        _couponBgView.userInteractionEnabled = YES;
        WEAKSELF;
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithActionBlock:^(id  _Nonnull sender) {
            [__weakSelf endEditing:YES];
            __weakSelf.hidden = YES;
            FMPayMentCouponSelectVC *vc = [[FMPayMentCouponSelectVC alloc] init];
            vc.selectedModel = __weakSelf.selectedCoupon;
            vc.dataArray = __weakSelf.availableCoupons;
            vc.closePage = ^{
                __weakSelf.hidden = NO;
            };
            vc.couponSelectBlock = ^(FMCouponTableModel *selectedCoupon) {
                __weakSelf.hidden = NO;
                __weakSelf.selectedCoupon = selectedCoupon;
                [__weakSelf configCouponNameAndValueShow];
                __weakSelf.askPrice = __weakSelf.askPrice;
            };
            [[FMHelper getCurrentVC] presentViewController:vc animated:YES completion:nil];
        }];
        [_couponBgView addGestureRecognizer:tap];
    }
    return _couponBgView;
}

- (UILabel *)couponNameLB {
    if (!_couponNameLB) {
        _couponNameLB = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(12) textColor:FMNavColor backgroundColor:FMClearColor numberOfLines:1];
        _couponNameLB.text = @"请选择";
    }
    return _couponNameLB;
}

- (UILabel *)couponValueLB {
    if (!_couponValueLB) {
        _couponValueLB = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(12) textColor:FMNavColor backgroundColor:FMClearColor numberOfLines:1];
    }
    return _couponValueLB;
}

- (UIImageView *)couponArrowImgV {
    if (!_couponArrowImgV) {
        _couponArrowImgV = [[UIImageView alloc] initWithImage:[UIImage imageWithTintColor:FMNavColor blendMode:kCGBlendModeDestinationIn WithImageObject:ImageWithName(@"userCenter_arrow")]];
    }
    return _couponArrowImgV;
}

- (ZLTagLabel *)firstRechargeLabel {
    if (!_firstRechargeLabel) {
        _firstRechargeLabel = [[ZLTagLabel alloc] init];
        _firstRechargeLabel.font = FontWithSize(10);
        _firstRechargeLabel.textColor = ColorWithHex(0x8B4513);
        _firstRechargeLabel.textAlignment = NSTextAlignmentCenter;
        _firstRechargeLabel.hidden = YES;
    }
    return _firstRechargeLabel;
}

- (FMAskCodeStockResultTableView *)stockResultView {
    if (!_stockResultView) {
        _stockResultView = [[FMAskCodeStockResultTableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
        _stockResultView.backgroundColor = UIColor.up_contentBgColor;
        WEAKSELF
        _stockResultView.stockSelectBlock = ^(FMSearchStockModel * _Nonnull model) {
            __weakSelf.stockTF.text = [NSString stringWithFormat:@"%@",model.name];
            __weakSelf.choosedStockModel = model;
            __weakSelf.resultBackView.hidden = YES;
        };
    }
    return _stockResultView;
}

- (UIView *)resultBackView {
    if (!_resultBackView) {
        _resultBackView = [[UIView alloc] init];
        _resultBackView.hidden = YES;
    }
    return _resultBackView;
}

@end
