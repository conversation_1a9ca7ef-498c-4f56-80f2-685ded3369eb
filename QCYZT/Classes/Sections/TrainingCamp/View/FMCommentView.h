//
//  FMCommentView.h
//  QCYZT
//
//  Created by th on 17/2/23.
//  Copyright © 2017年 sdcf. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "FMTextView.h"

@interface FMCommentView : UIView

@property (nonatomic, weak) UILabel *publisLb;
@property (nonatomic, copy) NSString *placeholderText;
@property (nonatomic, weak) FMTextView *textView;

@property (nonatomic, assign) NSInteger limitWordNum;
@property (nonatomic, weak) UIButton *publishBtn;
/// 发布按钮点击回调
@property (nonatomic,copy) void (^publishBlock) (NSString *content);

/// 支持表情
@property(nonatomic,assign) BOOL supportEmoji;

@end

