//
//  FMBigCastHomePageViewController.m
//  QCYZT
//
//  Created by th on 17/1/8.
//  Copyright © 2017年 sdcf. All rights reserved.
//

#import "FMBigCastHomePageViewController.h"
#import "FMBigcastHomePageNoteViewController.h"
#import "FMBigcastHomePageAskCodeViewController.h"
#import "FMBigcastHomePageCourseViewController.h"
#import "FMBigcastHomePageLiveViewController.h"
#import "FMOuterTableView.h"
#import "BigCastDetailModel.h"
#import "FMBigCastHomepageBottomView.h"
#import "FMDetailBottomView.h"
#import "FMAskCodePopView.h"
#import "FMPayTool.h"
#import "FMDakaHomePageHeaderView.h"
#import "FMPublishAlertViewController.h"
#import "HttpRequestTool+Daka.h"
#import "FMBigCastNoteMainViewController.h"
#import "FMHomePageNoteVC.h"
#import "FMMainSearchViewController.h"
#import "FMBigCastFocusReminderView.h"
#import "NSObject+FBKVOController.h"

#define kDetailBottomViewHeight (60 + UI_SAFEAREA_BOTTOM_HEIGHT)

@interface FMBigCastHomePageViewController() <UITableViewDelegate, UITableViewDataSource, FMInnerTableVCDelegate, SGPageTitleViewDelegate, SGPageContentCollectionViewDelegate, FMBigCastHomepageBottomViewDelegate>
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UIImageView *topBgImgV;
@property (nonatomic, strong) UIButton *searchBtn;
@property (nonatomic,strong) FMOuterTableView *tableView;

@property (nonatomic,strong) FMDakaHomePageHeaderView *headerView;
@property (nonatomic, strong) SGPageTitleView *pageTitleView;
@property (nonatomic, strong) SGPageContentCollectionView *cellInnerScrollView;
@property (nonatomic, strong) FMBigCastHomepageBottomView *bottomView;
@property (nonatomic, strong) FMAskCodePopView *commentView;

@property (nonatomic, copy) NSString *questionContent;
@property (nonatomic, strong) FMSearchStockModel *choosedStockModel;
@property (nonatomic,strong) NSArray *titleList;
@property (nonatomic, weak) FMInnerTableViewController *showingVC;
@property (nonatomic, strong) UITableViewCell *contentCell;
@property (nonatomic,strong) BigCastDetailModel *detailModel;
@property (nonatomic, strong) UIButton *bottomPublishBtn;

@property (nonatomic, strong) FMBigCastFocusReminderView *focusReminderView;
// 页面浏览计时
/// 计时数
@property (nonatomic, assign) NSInteger pageBrowseTime;
/// 关注提醒Flag， 0 停止  1运行 2暂停
@property (nonatomic, assign) NSInteger focusReminderFlag;

@end

@implementation FMBigCastHomePageViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.view.backgroundColor = UIColor.up_contentBgColor;
    
    [self.view addSubview:self.topBgImgV];
    [self.topBgImgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.top.equalTo(@0);
        make.height.equalTo(@(UI_Relative_WidthValue(200)));
    }];
    
    if (![self.userId isEqualToString:[FMUserDefault getUserId]]) {
        [self.view addSubview:self.bottomView];
        [self.bottomView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.bottom.equalTo(@0);
            make.height.equalTo(@(kDetailBottomViewHeight));
        }];
        self.bottomView.hidden = YES;
        self.bottomView.delegate = self;
    }
   
    [self.view addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(@0);
        make.top.equalTo(@(UI_SAFEAREA_TOP_HEIGHT));
        if (![self.userId isEqualToString:[FMUserDefault getUserId]]) {
            make.bottom.equalTo(self.bottomView.mas_top);
        } else {
            make.bottom.equalTo(self.view.mas_bottom).offset(-UI_SAFEAREA_BOTTOM_HEIGHT);
        }
    }];
    
    [self.view addSubview:self.focusReminderView];
    [self.focusReminderView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(15);
        make.right.equalTo(-15);
        make.bottom.equalTo(self.tableView.mas_bottom).offset(-20);
        make.height.equalTo(70);
    }];
    self.focusReminderView.hidden = YES;
    
    if ([self.userId isEqualToString:[FMUserDefault getUserId]]) {
        [self.view addSubview:self.bottomPublishBtn];
        [self.bottomPublishBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(@(-15));
            make.bottom.equalTo(@(-20 - UI_SAFEAREA_BOTTOM_HEIGHT));
            make.size.equalTo(@(CGSizeMake(50, 50)));
        }];
    }
    
    [self requestDakaInfo];
    
    self.pageBrowseTime = 0;
    self.focusReminderFlag = 1;
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(appWillResignActive) name:UIApplicationWillResignActiveNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(appDidBecomeActive) name:UIApplicationDidBecomeActiveNotification object:nil];
}

- (void)dealloc {
    FMLog(@"%s", __func__);
}

- (void)viewWillAppear:(BOOL)animated {
    self.selfNavigationBarHidden = YES;
    [super viewWillAppear:animated];
    
    if (self.focusReminderFlag == 2) {
        self.focusReminderFlag = 1;
    }
}

- (void)viewDidDisappear:(BOOL)animated {
    [super viewDidDisappear:animated];
    
    if (self.focusReminderFlag == 1) {
        self.focusReminderFlag = 2;
    }
}

- (void)publishBtnClick {
    [FMHelper checkLoginStatusNeedJumpToLoginWithBackBlock:^(FMUserModel *userInfoModel) {
        FMPublishAlertViewController *vc = [[FMPublishAlertViewController alloc] init];
        vc.isHomePage = YES;
        vc.modalPresentationStyle = UIModalPresentationFullScreen | UIModalPresentationOverCurrentContext;
        [[FMAppDelegate shareApp].main presentViewController:vc animated:NO completion:nil];
    }];
}

// 搜索按钮
- (void)searchBtnClick {
    FMMainSearchViewController *vc = [[FMMainSearchViewController alloc] initWithDakaModel:self.detailModel];
    WEAKSELF
    vc.gotoAskCode = ^{
        [__weakSelf bigCastHomepageBottomViewAskBtnDidClicked:__weakSelf.commentView btn:nil];
    };
    [self.navigationController pushViewController:vc animated:YES];
}

#pragma mark - SGPageTitleViewDelegate
- (void)pageTitleView:(SGPageTitleView *)pageTitleView selectedIndex:(NSInteger)selectedIndex {
    FMInnerTableViewController *subVC = self.childViewControllers[selectedIndex];
    _showingVC = subVC;
    if ([subVC isKindOfClass:[FMBigCastNoteMainViewController class]]){
        for (FMBigcastHomePageNoteViewController *vc in subVC.childViewControllers) {
            vc.delegate = self;
        }
    }
    [self.cellInnerScrollView setPageContentCollectionViewCurrentIndex:selectedIndex];
}

#pragma mark - SGPageContentCollectionViewDelegate
- (void)pageContentCollectionView:(SGPageContentCollectionView *)pageContentCollectionView progress:(CGFloat)progress originalIndex:(NSInteger)originalIndex targetIndex:(NSInteger)targetIndex {
    FMInnerTableViewController *subVC = self.childViewControllers[targetIndex];
    _showingVC = subVC;
    [self.pageTitleView setPageTitleViewWithProgress:progress originalIndex:originalIndex targetIndex:targetIndex];
}

#pragma mark - TableView Delegate/Datasource
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return 1;
}
- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    return self.contentCell;
}
- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    CGFloat bottomHeight = UI_SAFEAREA_BOTTOM_HEIGHT;
    if (![self.userId isEqualToString:[FMUserDefault getUserId]]) {
        if (self.detailModel.attestationType.integerValue == 2 && [self.detailModel.answerPrice integerValue] > 0) {
            bottomHeight = kDetailBottomViewHeight;
        }
    }
    return UI_SCREEN_HEIGHT - UI_SegmentControl_Height - UI_SAFEAREA_TOP_HEIGHT - bottomHeight;
}
- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    return self.pageTitleView;
}
- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    if (self.pageTitleView) {
        return UI_SegmentControl_Height;
    }
    return CGFLOAT_MIN;
}
- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    return CGFLOAT_MIN;
} 

#pragma mark - UIScrollView Delegate
- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    if (scrollView == self.tableView) {
        CGFloat topCellOffset = [self.tableView rectForSection:0].origin.y;
        
        // 如果里层tableView的偏移量大于0，将外层tableView的偏移量定在tableTopViewHeight，保持悬停
        if ([self.showingVC isKindOfClass:[FMBigCastNoteMainViewController class]]) {
            FMBigCastNoteMainViewController *vc = (FMBigCastNoteMainViewController *)self.showingVC;
            if (vc.showVC.tableView.contentOffset.y > 0) {
                self.tableView.contentOffset = CGPointMake(0, topCellOffset);
            }
        } else {
            if (self.showingVC.tableView.contentOffset.y > 0) {
                self.tableView.contentOffset = CGPointMake(0, topCellOffset);
            }
        }
        
        //如果外层tableView偏移量小于tableTopViewHeight（也就是头部视图正在显示），发出通知让每个子tableView的偏移量变成0
        CGFloat offSetY = self.tableView.contentOffset.y;
        if (offSetY < topCellOffset) {
            for (FMInnerTableViewController *VC in self.childViewControllers) {
                if([VC isKindOfClass:[FMBigCastNoteMainViewController class]]) {
                    FMBigCastNoteMainViewController *vc = (FMBigCastNoteMainViewController *)VC;
                    vc.showVC.tableView.contentOffset = CGPointZero;
                } else {
                    VC.tableView.contentOffset = CGPointZero;
                }
            }
        }
    }
}

#pragma mark - DASInnerTableVCDelegate
- (void)innerTableVCTableviewScroll:(UITableView *)innerTableview {
    CGFloat tableTopViewHeight = [self.tableView rectForSection:0].origin.y;
    // 如果外层tableView偏移量小于tableTopViewHeight（也就是头部视图正在显示），将子tableView的偏移量变成0
    if (self.tableView.contentOffset.y < tableTopViewHeight) {
        innerTableview.contentOffset = CGPointZero;
        innerTableview.showsVerticalScrollIndicator = NO;
    } else {
        innerTableview.showsVerticalScrollIndicator = YES;
    }
}

#pragma mark - FMBigCastHomepageBottomViewDelegate
- (void)bigCastHomepageBottomViewAskBtnDidClicked:(UIView *)bottomView btn:(UIButton *)askBtn {
    // 问股按钮点击处理 - 复用原来的评论逻辑
    bottomView.userInteractionEnabled = NO;
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        bottomView.userInteractionEnabled = YES;
    });
    
    if ([FMHelper checkLoginStatus]) {
        if ([self.detailModel.userid isEqualToString:[FMUserDefault getUserId]]) {
            [SVProgressHUD showInfoWithStatus:@"自己不能向自己提问"];
            return ;
        }
        
        if (self.detailModel.windowType == 2) {
            [FMHelper showVIPAlertWithType:VIPReadTypeAskCode needVip:self.detailModel.needVip authority:@"0" price:[self.detailModel.answerPrice floatValue] showAlert:YES clickSure:nil clickCancel:nil clickSureDismiss:YES];
        } else {
            [self gotoPay];
        }
    }
}

#pragma mark - NSNotification
- (void)appWillResignActive {
    if (self.focusReminderFlag == 1) {
        self.focusReminderFlag = 2;
    }
}

- (void)appDidBecomeActive {
    if (self.focusReminderFlag == 2) {
        self.focusReminderFlag = 1;
    }
}

#pragma mark - HTTP
- (void)requestDakaInfo {
    WEAKSELF
    [HttpRequestTool getDakaDetailWithUserId:self.userId start:^{
        [SVProgressHUD show];
    } failure:^{
        [SVProgressHUD dismiss];
        [self.view showReloadNetworkViewWithBlock:^{
            [__weakSelf requestDakaInfo];
        }];
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            [SVProgressHUD dismiss];
            
            self.detailModel = [BigCastDetailModel modelWithJSON:dic[@"data"]];

            // 底部视图
            if (![self.userId isEqualToString:[FMUserDefault getUserId]]) {
                // 设置模型数据，让底部视图自己处理显示逻辑
                self.bottomView.model = self.detailModel;

                // 判断是否需要显示底部视图
                BOOL shouldShowBottomView = NO;

                // 如果有问股功能或私信功能，就显示底部视图
                if ([self.detailModel.answerPrice integerValue] > 0 && self.detailModel.attestationType.integerValue == 2) {
                    shouldShowBottomView = YES;
                }

                // 私信按钮显示逻辑 - 按照用户提供的逻辑
                BOOL msgBtnHidden = (!(self.detailModel.letterPrice.integerValue > 0) && (self.detailModel.attestationType.integerValue == 2)) || [FMHelper getIAPPayStatus];
                if (!msgBtnHidden) {
                    shouldShowBottomView = YES;
                }

                if (shouldShowBottomView) {
                    self.bottomView.hidden = NO;
                    [self.bottomView mas_updateConstraints:^(MASConstraintMaker *make) {
                        make.height.equalTo(@(kDetailBottomViewHeight));
                    }];
                } else {
                    self.bottomView.hidden = YES;
                    [self.bottomView mas_updateConstraints:^(MASConstraintMaker *make) {
                        make.height.equalTo(@(UI_SAFEAREA_BOTTOM_HEIGHT));
                    }];
                }
            }
            
            // 标题
            if (![self.userId isEqualToString:[FMUserDefault getUserId]]) {
                self.titleLabel.text = @"大咖主页";
            }
        
            // 头部试图
            self.headerView.model = self.detailModel;
            CGFloat headerHeight = [self.headerView systemLayoutSizeFittingSize:UILayoutFittingCompressedSize].height + 1;
            self.headerView.frame = CGRectMake(0, 0, UI_SCREEN_WIDTH, ceil(headerHeight));
            self.tableView.tableHeaderView = self.headerView;
            
            self.searchBtn.hidden = !(self.detailModel.attestationType.integerValue > 0);
            
            [self configPageTitle];
            
            [self.tableView reloadData];
            
            [self requestBanner];
            
            [self pageBrowseTimeCountDownWithStartTime:[dic[@"systemTime"] longLongValue]];
        } else {
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
            [self.view showReloadNetworkViewWithBlock:^{
                [__weakSelf requestDakaInfo];
            }];
        }
    }];
}

- (void)requestBanner {
    [HttpRequestTool getDakaBannerWithUserId:self.userId start:^{
    } failure:^{
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            self.detailModel.banners = [NSArray modelArrayWithClass:[BigCastDetailBannerModel class] json:dic[@"data"]];
            
            self.headerView.model = self.detailModel;
            CGFloat headerHeight = [self.headerView systemLayoutSizeFittingSize:UILayoutFittingCompressedSize].height + 1;
            self.headerView.frame = CGRectMake(0, 0, UI_SCREEN_WIDTH, ceil(headerHeight));
            self.tableView.tableHeaderView = self.headerView;
            
            [self.tableView reloadData];
        }
    }];
}

#pragma mark - UIAlertView Delegate
- (void)alertView:(UIAlertView *)alertView clickedButtonAtIndex:(NSInteger)buttonIndex {
    if (buttonIndex == 1) {
        WEAKSELF;
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [HttpRequestTool questionAskWithAnswerId:__weakSelf.detailModel.userid content:__weakSelf.questionContent  type:__weakSelf.commentView.type goodsId:__weakSelf.commentView.couponId stockCode:__weakSelf.choosedStockModel.oldStockCode stockName:__weakSelf.choosedStockModel.name start:^{
                [SVProgressHUD show];
            } failure:^{
                [SVProgressHUD showErrorWithStatus:@"网络不给力"];
            } success:^(NSDictionary *dic) {
                if ([dic[@"status"] isEqualToString:@"1"]) {
                    [SVProgressHUD showSuccessWithStatus:@"您的提问已成功发送，请注意接收回复通知"];
                    __weakSelf.commentView.textView.text = @"";
                    __weakSelf.questionContent = nil;
                    __weakSelf.choosedStockModel = nil;
                    __weakSelf.commentView = nil;
                    [[NSNotificationCenter defaultCenter] postNotificationName:kAskCodeSuccess object:nil];
                } else {
                    [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
                }
            }];
        });
    } else {
        self.questionContent = nil;
        self.choosedStockModel = nil;
    }
}


#pragma mark - Private
- (void)configPageTitle {
    SGPageTitleViewConfigure *configure = [SGPageTitleViewConfigure pageTitleViewConfigure];
    configure.titleColor = UIColor.up_textSecondaryColor;
    configure.titleFont = [FMHelper scaleFont:17.0];
    configure.titleSelectedColor = UIColor.up_textPrimaryColor;
    configure.titleSelectedFont = [FMHelper scaleBoldFont:17.0];
    configure.indicatorStyle = SGIndicatorStyleFixed;
    configure.indicatorColor = FMNavColor;
    configure.indicatorFixedWidth = 18;
    configure.indicatorHeight = 3;
    configure.indicatorCornerRadius = 1.5;
    configure.titleAdditionalWidth = 30;
    configure.showBottomSeparator = NO;
    
    if (self.detailModel.attestationType.integerValue == 2) { // 投顾
        if (self.detailModel.isLiveNum > 0) {
            self.titleList = @[@"主页", @"专栏", @"直播", @"课程",@"问股"];
        } else {
            self.titleList = @[@"主页", @"专栏", @"课程",@"问股"];
        }
    }  else { // 其他用户
        if ([self.detailModel.userid integerValue] == 18) { // 千策君单独加上课程
            if (self.detailModel.isLiveNum > 0) {
                self.titleList = @[@"主页", @"专栏", @"直播",@"课程"];
            } else {
                self.titleList = @[@"主页", @"专栏", @"课程"];
            }
        } else {
            if (self.detailModel.isLiveNum > 0) {
                self.titleList = @[@"主页", @"专栏", @"直播"];
            } else {
                self.titleList = @[@"主页", @"专栏"];
            }
        }
    }
    
    
    self.pageTitleView = [SGPageTitleView pageTitleViewWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, 45) delegate:self titleNames:self.titleList configure:configure];
    self.pageTitleView.backgroundColor = UIColor.up_contentBgColor;
    CGFloat bottomHeight = UI_SAFEAREA_BOTTOM_HEIGHT;
    if (![self.userId isEqualToString:[FMUserDefault getUserId]]) {
        if ([self.detailModel.answerPrice integerValue] > 0 && self.detailModel.attestationType.integerValue == 2) {
            bottomHeight = kDetailBottomViewHeight;
        }
    }
    self.cellInnerScrollView = [[SGPageContentCollectionView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, UI_SCREEN_HEIGHT - UI_SegmentControl_Height - UI_SAFEAREA_TOP_HEIGHT - bottomHeight) parentVC:self childVCs:[self getChildVCs]];
    self.cellInnerScrollView.delegatePageContentCollectionView = self;
    // 处理侧滑返回失效
    if (self.navigationController.interactivePopGestureRecognizer) {
        [self.cellInnerScrollView.collectionView.panGestureRecognizer requireGestureRecognizerToFail:self.navigationController.interactivePopGestureRecognizer];
    }

//    // 有直播中的数据定位在直播列表
//    if (self.detailModel.isLive == 1) {
//        [self.pageTitleView setSelectedIndex:1];
//    }
    if (self.firstLevelIndex > 0) {
        [self.pageTitleView setSelectedIndex:self.firstLevelIndex];
        self.firstLevelIndex = 0;
    }
    
    [self.contentCell.contentView addSubview:self.cellInnerScrollView];
}

- (NSArray *)getChildVCs {
//    BOOL isSelfPage = [[FMUserDefault getUserId] isEqualToString:self.userId];
    // 主页
    FMBigCastNoteMainViewController *noteVC1 = [[FMBigCastNoteMainViewController alloc] init];
    noteVC1.attestationType = self.detailModel.attestationType.integerValue;
    noteVC1.userId = self.userId;
    noteVC1.chooseIndex = self.secondLevelIndex;
    
    // 专栏
    FMBigcastHomePageNoteViewController *noteVC2 = [[FMBigcastHomePageNoteViewController alloc] initWithUserId:self.userId];
    noteVC2.onlyNote = YES;
    noteVC2.delegate = self;
    // 视频
    FMBigcastHomePageLiveViewController *liveVC = [[FMBigcastHomePageLiveViewController alloc] init];
    liveVC.authorId = self.userId;
    liveVC.delegate = self;
    // 问股
    FMBigcastHomePageAskCodeViewController *askCodeVC = [[FMBigcastHomePageAskCodeViewController alloc] init];
    askCodeVC.answerUserId = self.userId;
    askCodeVC.delegate = self;
    // 课程
    FMBigcastHomePageCourseViewController *courseVC = [[FMBigcastHomePageCourseViewController alloc] init];
    courseVC.authorId = self.userId;
    courseVC.delegate = self;

    if (self.detailModel.attestationType.integerValue == 2) { // 投顾
        if (self.detailModel.isLiveNum > 0) {
            return @[noteVC1, noteVC2, liveVC, courseVC, askCodeVC];
        } else {
            return @[noteVC1, noteVC2, courseVC, askCodeVC];
        }
    }  else { // 其他用户
        if ([self.detailModel.userid integerValue] == 18) { // 千策君单独加上课程
            if (self.detailModel.isLiveNum > 0) {
                return @[noteVC1, noteVC2, liveVC, courseVC];
            } else {
                return @[noteVC1, noteVC2, courseVC];
            }
        } else {
            if (self.detailModel.isLiveNum > 0) {
                return @[noteVC1, noteVC2, liveVC];
            } else {
                return @[noteVC1, noteVC2];
            }
        }
    }
}

- (void)backArrowClicked {
    [self.navigationController popViewControllerAnimated:YES];
}

- (void)gotoPay {
    WEAKSELF;
    // 2.余额够的话判断风险提示书
    [[FMPayTool payTool] judgeConfirmOrderStatusWithDakaId:__weakSelf.detailModel.userid certCode:__weakSelf.detailModel.certCode clickView:__weakSelf.bottomView confirmOperation:^{
        // 3.显示提问框
        UIWindow *keyWindow = [UIApplication sharedApplication].keyWindow;
        [keyWindow addSubview:__weakSelf.commentView];
        __weakSelf.commentView.publisLb.textColor = UIColor.up_textPrimaryColor;
        __weakSelf.commentView.publisLb.text = [NSString stringWithFormat:@"向%@老师提问", self.detailModel.userName];
        __weakSelf.commentView.placeholderText = @"输入您的问题。若已买入该股票，可告知“买入价格”和“仓位情况”，更便于老师根据您的实际情况给予建议。\n若超过预期时间未解答，提问费用或卡券将会自动退回。";
        __weakSelf.commentView.askPrice = __weakSelf.detailModel.answerPrice;
        
        __weakSelf.commentView.bignameId = __weakSelf.detailModel.userid;
        __weakSelf.commentView.consumeType = 2;
        [__weakSelf.commentView requestCouponList];
        __weakSelf.commentView.publishAskCodeBlock = ^(NSString *content, FMSearchStockModel *stockModel) {
            __weakSelf.questionContent = content;
            __weakSelf.choosedStockModel = stockModel;
            if (__weakSelf.commentView.couponId.length == 0) {
                // 没有选择优惠券 先校验余额
                [[FMPayTool payTool] compareCoinRemainderWithConsume:__weakSelf.detailModel.answerPrice clickView:self.bottomView completeBlock:^(NSString *coinRemainder) {
                    // 查询问股回答所需时长
                    [__weakSelf checkWaitTime];
                }];
            } else {
                // 查询问股回答所需时长
                [__weakSelf checkWaitTime];
            }
        };
    }];
}

/// 查询问股回答所需时长
- (void)checkWaitTime {
    [HttpRequestTool questionCheckWaitTimeWithAnswerId:self.detailModel.userid start:^{
        [SVProgressHUD show];
    } failure:^{
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            [SVProgressHUD dismiss];
            UIAlertView *alertView = [[UIAlertView alloc] initWithTitle:nil
                                                                message:dic[@"data"]
                                                               delegate:self
                                                      cancelButtonTitle:@"取消"
                                                      otherButtonTitles:@"确定", nil];
            [alertView show];
        } else {
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
        }
    }];
}

/// 页面浏览计时
- (void)pageBrowseTimeCountDownWithStartTime:(long long)startTime {
    WEAKSELF
    [self.KVOController observe:[CountDownShareInstance shareInstance] keyPath:DifferentValue options:NSKeyValueObservingOptionNew|NSKeyValueObservingOptionOld block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        if ([change objectForKey:NSKeyValueChangeNewKey]) {
            __weakSelf.pageBrowseTime ++;
//            FMLog(@"大咖页面浏览时长%zd秒", __weakSelf.pageBrowseTime);
            
            if (__weakSelf.focusReminderFlag == 1) {
                if (__weakSelf.pageBrowseTime >= 10) {
                    [__weakSelf judgeShowFocusReminder];
                    __weakSelf.focusReminderFlag = 0;
                }
            }
        }
    }];
}

/// 判断是否显示关注提醒
- (void)judgeShowFocusReminder {
    if (self.detailModel && ![[FMUserDefault getUserId] isEqualToString:self.detailModel.userid] && ![[FMUserDataSyncManager sharedManager] isDakaNoticed:self.detailModel.userid]) {
        self.focusReminderView.hidden = NO;
        self.focusReminderView.model = self.detailModel;
    } else {
        self.focusReminderView.hidden = YES;
    }
}

#pragma mark - Getter/Setter
- (UIImageView *)topBgImgV {
    if (!_topBgImgV) {
        _topBgImgV = [[UIImageView alloc] initWithImage:FMImgInBundle(@"大咖/主页顶部")];
        _topBgImgV.userInteractionEnabled = YES;
        
        UIView *navView = [[UIView alloc] init];
        [_topBgImgV addSubview:navView];
        [navView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(@0);
            make.top.equalTo(@(UI_STATUS_HEIGHT));
            make.height.equalTo(@(UI_NAVBAR_HEIGHT));
        }];
        navView.backgroundColor = FMClearColor;
        
        UIButton *backArrow = [[UIButton alloc] initWithFrame:CGRectZero font:nil normalTextColor:nil backgroundColor:FMClearColor title:nil image:FMImgInBundle(@"导航/亮白暗灰返回") target:self action:@selector(backArrowClicked)];
        [navView addSubview:backArrow];
        [backArrow mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.top.bottom.equalTo(@0);
            make.width.equalTo(@(UI_NAVBAR_HEIGHT));
        }];
        
        UILabel *titleLabel = [[UILabel alloc] initWithFrame:CGRectZero font:[FMHelper scaleBoldFont:18] textColor:FMWhiteColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
        [navView addSubview:titleLabel];
        [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.center.equalTo(@0);
        }];
        if ([self.userId isEqualToString:[FMUserDefault getUserId]]) {
            titleLabel.text = @"我的创作";
        }
        self.titleLabel = titleLabel;
        
        UIButton *searchBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [searchBtn setImage:FMImgInBundle(@"导航/亮白暗灰搜索") forState:UIControlStateNormal];
        [searchBtn addTarget:self action:@selector(searchBtnClick) forControlEvents:UIControlEventTouchUpInside];
        searchBtn.hidden = YES;
        [navView addSubview:searchBtn];
        [searchBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(navView);
            make.right.equalTo(navView.mas_right).offset(-5);
            make.size.equalTo(CGSizeMake(38, 38));
        }];
        self.searchBtn = searchBtn;
    }
    
    return _topBgImgV;
}

- (FMOuterTableView *)tableView {
    if (!_tableView) {
        _tableView = [[FMOuterTableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain delegate:self dataSource:self viewController:self];
        _tableView.backgroundColor = FMClearColor;
        _tableView.showsVerticalScrollIndicator = NO;
        _tableView.tableFooterView = [[UIView alloc] init];
    }
    
    return _tableView;
}

- (FMDakaHomePageHeaderView *)headerView {
    if (!_headerView) {
        _headerView = [[FMDakaHomePageHeaderView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, 0)];
        _headerView.backgroundColor = FMClearColor;
        WEAKSELF;
        _headerView.askBtnClickBlock = ^(UIButton * _Nonnull sender) {
            [__weakSelf bigCastHomepageBottomViewAskBtnDidClicked:__weakSelf.bottomView btn:sender];
        };
        _headerView.refreshBlock = ^{
            __weakSelf.headerView.model = __weakSelf.detailModel;
            CGFloat headerHeight = [__weakSelf.headerView systemLayoutSizeFittingSize:UILayoutFittingCompressedSize].height + 1;
            __weakSelf.headerView.frame = CGRectMake(0, 0, UI_SCREEN_WIDTH, ceil(headerHeight));
            __weakSelf.tableView.tableHeaderView = __weakSelf.headerView;
            [__weakSelf.tableView reloadData];
        };
    }
    return _headerView;
}

- (UITableViewCell *)contentCell {
    if (!_contentCell) {
        UITableViewCell *cell = [[UITableViewCell alloc] init];
        cell.contentView.backgroundColor = UIColor.up_contentBgColor;
        cell.selectionStyle = UITableViewCellSelectionStyleNone;
        _contentCell = cell;
    }
    return _contentCell;
}

- (FMBigCastHomepageBottomView *)bottomView {
    if (!_bottomView) {
        _bottomView = [[FMBigCastHomepageBottomView alloc] init];
    }
    return _bottomView;
}

- (FMAskCodePopView *)commentView {
    if (!_commentView) {
        _commentView = [[FMAskCodePopView alloc] initWithFrame:[UIScreen mainScreen].bounds];
    }
    return _commentView;
}

- (UIButton *)bottomPublishBtn {
    if (!_bottomPublishBtn) {
        UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
        [btn setImage:ImageWithName(@"bottom_publish") forState:UIControlStateNormal];
        [btn addTarget:self action:@selector(publishBtnClick) forControlEvents:UIControlEventTouchUpInside];
        _bottomPublishBtn = btn;
        
    }
    return _bottomPublishBtn;
}

- (FMBigCastFocusReminderView *)focusReminderView {
    if (!_focusReminderView) {
        _focusReminderView = [FMBigCastFocusReminderView new];
    }
    
    return _focusReminderView;
}

@end
